use std::{fs, path::PathBuf};

pub fn get_jetbrains_config_dir() -> Option<PathBuf> {
    [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()]
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .map(|base_dir| base_dir.join("JetBrains"))
        .find(|path| path.exists())
}

pub fn get_vscode_files(id: &str) -> Option<Vec<PathBuf>> {
    let base_dirs = [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()];
    let global_patterns = [
        &["User", "globalStorage"] as &[&str],
        &["data", "User", "globalStorage"],
        &[id],
        &["data", id],
    ];
    let workspace_patterns = [
        &["User", "workspaceStorage"] as &[&str],
        &["data", "User", "workspaceStorage"],
    ];

    let vscode_dirs: Vec<PathBuf> = base_dirs
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .flat_map(|base_dir| {
            fs::read_dir(&base_dir)
                .into_iter()
                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                .filter(|entry| entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false))
                .flat_map(|entry| {
                    let entry_path = entry.path();

                    // Global storage patterns
                    let global_paths: Vec<PathBuf> = global_patterns
                        .iter()
                        .map(|pattern| {
                            pattern
                                .iter()
                                .fold(entry_path.clone(), |path, segment| path.join(segment))
                        })
                        .collect();

                    // Workspace storage patterns - enumerate all subdirectories
                    let workspace_paths: Vec<PathBuf> = workspace_patterns
                        .iter()
                        .flat_map(|pattern| {
                            let workspace_base = pattern
                                .iter()
                                .fold(entry_path.clone(), |path, segment| path.join(segment));
                            if workspace_base.exists() {
                                fs::read_dir(&workspace_base)
                                    .into_iter()
                                    .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                                    .filter(|entry| {
                                        entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false)
                                    })
                                    .map(|entry| entry.path())
                                    .collect::<Vec<_>>()
                            } else {
                                Vec::new()
                            }
                        })
                        .collect();

                    // Extension patterns - find Augment extension folder
                    let extension_paths: Vec<PathBuf> = {
                        let extensions_dir = entry_path.join("extensions");
                        if extensions_dir.exists() {
                            fs::read_dir(&extensions_dir)
                                .into_iter()
                                .flat_map(|entries| entries.filter_map(|entry| entry.ok()))
                                .filter(|entry| {
                                    entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false)
                                })
                                .filter(|entry| {
                                    entry
                                        .file_name()
                                        .to_string_lossy()
                                        .starts_with("augment.vscode-augment")
                                })
                                .map(|entry| entry.path().join("out"))
                                .collect()
                        } else {
                            Vec::new()
                        }
                    };

                    global_paths
                        .into_iter()
                        .chain(workspace_paths)
                        .chain(extension_paths)
                })
        })
        .filter(|path| path.exists())
        .collect();

    (!vscode_dirs.is_empty()).then_some(vscode_dirs)
}
