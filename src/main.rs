use clap::Parser;
use default_args::default_args;
use kill_tree::blocking::kill_tree;
use rusqlite::Connection;

use std::io::{self, Write};
use std::path::Path;
use sysinfo::System;

mod utils;
use utils::*;

mod vscode_extension_patcher;
use vscode_extension_patcher::*;

type Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;

#[derive(Parser)]
#[command(name = "augment-vip")]
#[command(about = "A tool for managing JetBrains and VSCode telemetry IDs")]
struct Args {
    /// Skip all pauses for user interaction
    #[arg(long)]
    no_pause: bool,

    /// Skip signing out of accounts
    #[arg(long)]
    no_signout: bool,

    /// Skip IDE termination
    #[arg(long)]
    no_terminate: bool,
}

fn main() {
    let args = Args::parse();

    // Need admin to apply many of the fixes
    #[cfg(target_family = "unix")]
    if let Err(e) = sudo2::escalate_if_needed() {
        eprintln!(
            "Warning: {}\nSudo permissions not granted, the application may not work properly!",
            e
        );
    }

    if let Err(e) = run(&args) {
        eprintln!("Error: {}", e);
        pause(&args);
        std::process::exit(1);
    }

    pause(&args);
}

fn pause(args: &Args) {
    if args.no_pause {
        return;
    }
    print!("\nPress Enter to exit...");
    io::stdout().flush().unwrap();
    io::stdin().read_line(&mut String::new()).unwrap();
}

fn terminate_ides() {
    for (pid, process) in System::new_all().processes() {
        let cmd_str = process
            .cmd()
            .join(" ".as_ref())
            .to_string_lossy()
            .to_string();
        if !cmd_str.contains("vscode") && !cmd_str.contains(".augmentcode") {
            continue;
        }
        if let Some(parent_pid) = process.parent() {
            let _ = kill_tree(parent_pid.as_u32());
        }
        let _ = kill_tree(pid.as_u32());
    }
}

const COUNT_VSCODE_STATE_DB_QUERY: &str =
    "SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%';";
const DELETE_VSCODE_STATE_DB_QUERY: &str = "DELETE FROM ItemTable WHERE key LIKE '%augment%';";

default_args! {
    fn clean_vscode_database(vscode_global_storage_path: &Path, file_name: &String = &"state.vscdb".to_string()) -> Result<()> {
        let state_db_path = vscode_global_storage_path.join(file_name);

        if !state_db_path.exists() {
            return Ok(());
        }

        let conn = Connection::open(&state_db_path)?;

        // Check how many rows would be deleted first
        let rows_to_delete: i64 = conn.prepare(COUNT_VSCODE_STATE_DB_QUERY)?.query_row([], |row| row.get(0))?;
        if rows_to_delete > 0 {
            println!("Found {} potential entries to remove from '{}'", rows_to_delete, state_db_path.file_name().unwrap_or_default().to_string_lossy());

            // Execute the delete query
            conn.execute(DELETE_VSCODE_STATE_DB_QUERY, [])?;

            println!("Successfully removed {} entries from '{}'", rows_to_delete, state_db_path.file_name().unwrap_or_default().to_string_lossy());
        }

        if file_name.ends_with(".backup") {
            return Ok(());
        }
        clean_vscode_database_(vscode_global_storage_path, &(file_name.to_string() + ".backup"))
    }
}

fn run(args: &Args) -> Result<()> {
    // if !args.no_terminate { terminate_ides(); }
    let mut programs_found = false;

    // Try to find and update JetBrains
    if let Some(_jetbrains_dir) = get_jetbrains_config_dir() {
        programs_found = true;
        // TODO: Implement jetbrains cleanup and extension modification
    } else {
        println!("JetBrains configuration directory not found");
    }

    // Try to find and update VSCode variants
    if let Some(vscode_dirs) = get_vscode_files("machineId") {
        programs_found = true;

        // for vscode_dir in &vscode_dirs {
        //     if !args.no_signout {
        //         clean_vscode_database!(vscode_dir)?;
        //     }
        // }

        // Patch VSCode extensions to disable telemetry
        patch_vscode_extensions(vscode_dirs);

        println!("All found VSCode variants' telemetry for AugmentCode has been disabled!");
    } else {
        println!("No VSCode variants found");
    }

    // Error only if no programs were found at all
    if !programs_found {
        return Err("No JetBrains or VSCode installations found".into());
    }

    Ok(())
}
