use std::{fs, path::PathBuf};

use regex::Regex;

const CRACKED_MASK: &str = ";console.log('Cracked by AugmentVip');";
const REPORT_REGEX: &str = r";super\.report\((?<var>.+).toVector\(\)\)";

fn patch_extension(path: &PathBuf) {
    // read file content
    let file_content = fs::read_to_string(path).unwrap();

    // find this section: <variable_name>.toVector() by regex. where <variable_name> is a match group
    let regex = Regex::new(REPORT_REGEX).unwrap();
    for captures in regex.captures_iter(&file_content) {
        let var = captures.name("var").unwrap().as_str();
        let statement = captures.get(0).unwrap().as_str();

        println!("Found var: {}", var);
        println!("Found statement: {}", statement);

        // check if the line contains the CRACKED_MASK
        if file_content.contains(CRACKED_MASK) {
            println!("Skipping {} because it's already cracked", var);
            continue;
        }

        // insert CRACKED_MASK before the statement
        let new_content =
            file_content.replace(statement, format!("{}{}", CRACKED_MASK, statement).as_str());
        fs::write(path, new_content).unwrap();
    }
}

pub fn patch_vscode_extensions(vscode_dirs: Vec<PathBuf>) {
    // find all directories that contains the file "extension.js"
    let extension_js_dirs = vscode_dirs
        .iter()
        .filter_map(|dir| {
            let extension_js = dir.join("extension.js");
            extension_js.exists().then_some(extension_js)
        })
        .collect::<Vec<_>>();

    // print all the directories that contain the file "extension.js"
    for dir in extension_js_dirs {
        println!("Found extension.js in {}", dir.display());
        patch_extension(&dir);
    }
}
