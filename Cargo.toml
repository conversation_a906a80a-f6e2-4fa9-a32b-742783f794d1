[package]
name = "augment-vip"
version = "0.3.1"
edition = "2021"

[dependencies]
uuid = { version = "1", features = ["v4"] }
dirs = "6"
base64 = "0.22"
serde_json = "1.0"
sha2 = "0.10"
rusqlite = { version = "0.36", features = ["bundled"] }
default-args = "1.0.0"
clap = { version = "4.5.39", features = ["derive"] }
sysinfo = "0.35"
kill_tree = "0.2"
regex = "1.0"

[target.'cfg(target_os = "windows")'.dependencies]
winreg = "0.55"

[target.'cfg(target_family = "unix")'.dependencies]
sudo2 = "0.2"

[build-dependencies]
embed-resource = "3"

[profile.release]
strip = true
opt-level = "z"
codegen-units = 1
lto = true
panic = "abort"
